"""
Pydantic schemas for the model service.
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime

# Basic transaction schemas
class TransactionType(str, Enum):
    """Enum for transaction types"""
    PAYMENT = "PAYMENT"
    TRANSFER = "TRANSFER"
    CASH_OUT = "CASH_OUT"
    DEBIT = "DEBIT"

class TransactionBase(BaseModel):
    """Base schema for transaction data"""
    transaction_id: Optional[str] = None
    step: int = Field(..., ge=1)
    type: TransactionType
    amount: float = Field(..., gt=0)
    nameOrig: str
    oldbalanceOrg: float = Field(..., ge=0)
    newbalanceOrig: float = Field(..., ge=0)
    nameDest: str
    oldbalanceDest: float = Field(..., ge=0)
    newbalanceDest: float = Field(..., ge=0)

class TransactionRequest(BaseModel):
    """Schema for transaction scoring request"""
    transactions: List[TransactionBase]

class RiskScore(BaseModel):
    """Schema for risk score response"""
    transaction_id: str
    risk: float = Field(..., ge=0, le=1)

class RiskScoreResponse(BaseModel):
    """Schema for risk score response"""
    results: List[RiskScore]

class HealthResponse(BaseModel):
    """Schema for health check response"""
    status: str
    version: str
    timestamp: datetime

class MetricsResponse(BaseModel):
    """Schema for metrics response"""
    metrics: str

class CaseStatus(str, Enum):
    """Case status enum"""
    OPEN = "open"
    CLOSED = "closed"
    PENDING = "pending"

class CaseTag(str, Enum):
    """Case tag enum"""
    FALSE_POSITIVE = "FP"
    CONFIRMED = "CONFIRMED"
    SUSPICIOUS = "SUSPICIOUS"
    NEEDS_REVIEW = "NEEDS_REVIEW"

class UserRole(str, Enum):
    """User role enum"""
    ANALYST = "analyst"
    AUDITOR = "auditor"
    ADMIN = "admin"

class TokenData(BaseModel):
    """Token data schema"""
    username: Optional[str] = None

class Token(BaseModel):
    """Token schema"""
    access_token: str
    token_type: str

class UserBase(BaseModel):
    """Base user schema"""
    username: str

class UserCreate(UserBase):
    """User creation schema"""
    password: str
    role: UserRole = UserRole.ANALYST

class UserResponse(UserBase):
    """User response schema"""
    id: int
    role: UserRole
    created_at: datetime

    class Config:
        orm_mode = True

class CaseBase(BaseModel):
    """Base case schema"""
    transaction_id: str
    tag: CaseTag
    comment: Optional[str] = None
    status: CaseStatus = CaseStatus.OPEN

class CaseCreate(CaseBase):
    """Case creation schema"""
    pass

class CaseUpdate(BaseModel):
    """Case update schema"""
    tag: Optional[CaseTag] = None
    comment: Optional[str] = None
    status: Optional[CaseStatus] = None

class CaseResponse(CaseBase):
    """Case response schema"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class CaseStatsResponse(BaseModel):
    """Case statistics response schema"""
    status_counts: Dict[str, int]
    tag_counts: Dict[str, int]
    total_cases: int

class AuditLogResponse(BaseModel):
    """Audit log response schema"""
    id: int
    case_id: int
    action: str
    metadata: Optional[Dict[str, Any]] = None
    timestamp: datetime

    class Config:
        orm_mode = True

class TransactionResponse(BaseModel):
    """Transaction response schema"""
    transaction_id: str
    step: int
    type: str
    amount: float
    name_orig: str
    old_balance_orig: float
    new_balance_orig: float
    name_dest: str
    old_balance_dest: float
    new_balance_dest: float
    risk_score: float
    is_fraud: Optional[int] = None
    created_at: datetime

    class Config:
        orm_mode = True
