"""
Model wrapper for fraud detection model.
"""
import os
import joblib
import pandas as pd
import numpy as np
from typing import Dict, List, Union, Any

class ModelWrapper:
    """
    Wrapper for the fraud detection model.
    Handles loading the model and preprocessing input data.
    """
    def __init__(self, model_path: str):
        """
        Initialize the model wrapper.
        
        Args:
            model_path: Path to the serialized model file
        """
        self.model = joblib.load(model_path)
        self.feature_columns = [
            'step', 'type', 'amount', 'oldbalanceOrg', 'newbalanceOrig',
            'oldbalanceDest', 'newbalanceDest'
        ]
        
    def _preprocess(self, transaction: Dict[str, Any]) -> pd.DataFrame:
        """
        Preprocess a transaction for prediction.
        
        Args:
            transaction: Dictionary containing transaction data
            
        Returns:
            DataFrame with preprocessed features
        """
        # Create a DataFrame with the transaction
        df = pd.DataFrame([transaction])
        
        # One-hot encode the transaction type
        transaction_types = ['CASH_OUT', 'DEBIT', 'PAYMENT', 'TRANSFER']
        for t_type in transaction_types:
            df[f'type_{t_type}'] = (df['type'] == t_type).astype(int)
        
        # Add merchant flag feature (if destination starts with 'M')
        if 'nameDest' in df.columns:
            df['merchantFlag'] = df['nameDest'].str.startswith('M').astype(int)
        else:
            df['merchantFlag'] = 0
            
        return df
    
    def predict_proba(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, float]]:
        """
        Predict fraud probability for a list of transactions.
        
        Args:
            transactions: List of transaction dictionaries
            
        Returns:
            List of dictionaries with transaction_id and risk score
        """
        results = []
        
        for transaction in transactions:
            # Preprocess the transaction
            df = self._preprocess(transaction)
            
            # Get the fraud probability (class 1)
            try:
                fraud_prob = float(self.model.predict_proba(df)[:, 1][0])
            except Exception:
                # Fallback if model prediction fails
                fraud_prob = 0.0
                
            # Create result with transaction_id and risk score
            result = {
                "transaction_id": transaction.get("transaction_id", 
                                                transaction.get("nameOrig", "unknown")),
                "risk": fraud_prob
            }
            results.append(result)
            
        return results
    
    def predict(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Make binary predictions for a list of transactions.
        
        Args:
            transactions: List of transaction dictionaries
            
        Returns:
            List of dictionaries with transaction_id and prediction (0 or 1)
        """
        results = []
        proba_results = self.predict_proba(transactions)
        
        for i, transaction in enumerate(transactions):
            # Get the probability result
            proba = proba_results[i]["risk"]
            
            # Apply threshold (0.5 by default)
            prediction = 1 if proba >= 0.5 else 0
            
            # Create result with transaction_id and prediction
            result = {
                "transaction_id": transaction.get("transaction_id", 
                                                transaction.get("nameOrig", "unknown")),
                "prediction": prediction,
                "risk": proba
            }
            results.append(result)
            
        return results
