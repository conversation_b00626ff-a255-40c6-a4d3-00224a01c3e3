"""
Main module for the ingest service.
Handles Kafka consumer, transaction validation, enrichment, and forwarding to model service.
"""
import os
import json
import time
import logging
import asyncio
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import prometheus_client
from prometheus_client import Counter, Histogram, generate_latest

from schemas import Ka<PERSON><PERSON>Message, TransactionBase, EnrichedTransaction, ModelServiceRequest
from utils import enrich_transaction

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ingest-service")

# Initialize FastAPI app
app = FastAPI(
    title="Fraud Detection Ingest Service",
    description="Service for ingesting and preprocessing transaction data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize metrics
MESSAGES_PROCESSED = Counter('ingest_messages_processed_total', 'Total number of messages processed')
MESSAGES_INVALID = Counter('ingest_messages_invalid_total', 'Total number of invalid messages')
PROCESSING_TIME = Histogram('ingest_processing_time_seconds', 'Time spent processing message')
MODEL_REQUESTS = Counter('ingest_model_requests_total', 'Total number of requests to model service')
MODEL_ERRORS = Counter('ingest_model_errors_total', 'Total number of errors from model service')
HIGH_RISK_ALERTS = Counter('ingest_high_risk_alerts_total', 'Total number of high risk alerts')

# Configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092")
KAFKA_TOPIC_TRANSACTIONS = os.getenv("KAFKA_TOPIC_TRANSACTIONS", "transactions")
KAFKA_TOPIC_INVALID = os.getenv("KAFKA_TOPIC_INVALID", "transactions.invalid")
MODEL_SERVICE_URL = os.getenv("MODEL_SERVICE_URL", "http://model-service:8000/score")
RISK_THRESHOLD = float(os.getenv("RISK_THRESHOLD", "0.8"))
SLACK_WEBHOOK_URL = os.getenv("SLACK_WEBHOOK_URL", "")

# Global variables
consumer = None
producer = None
websocket_clients = set()

@app.on_event("startup")
async def startup_event():
    """Initialize Kafka consumer and producer on startup"""
    global consumer, producer
    
    logger.info("Starting ingest service")
    
    # Initialize Kafka consumer
    consumer = AIOKafkaConsumer(
        KAFKA_TOPIC_TRANSACTIONS,
        bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
        group_id="ingest-service",
        auto_offset_reset="earliest",
        enable_auto_commit=True,
    )
    await consumer.start()
    logger.info(f"Kafka consumer started, listening on topic {KAFKA_TOPIC_TRANSACTIONS}")
    
    # Initialize Kafka producer
    producer = AIOKafkaProducer(
        bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS
    )
    await producer.start()
    logger.info("Kafka producer started")
    
    # Start background task for processing messages
    asyncio.create_task(process_messages())

@app.on_event("shutdown")
async def shutdown_event():
    """Stop Kafka consumer and producer on shutdown"""
    global consumer, producer
    
    logger.info("Shutting down ingest service")
    
    if consumer:
        await consumer.stop()
        logger.info("Kafka consumer stopped")
    
    if producer:
        await producer.stop()
        logger.info("Kafka producer stopped")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "version": "1.0.0",
        "timestamp": datetime.now()
    }

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest()

async def process_messages():
    """Process messages from Kafka"""
    global consumer, producer
    
    logger.info("Starting message processing loop")
    
    try:
        async for message in consumer:
            start_time = time.time()
            MESSAGES_PROCESSED.inc()
            
            try:
                # Parse message
                value = json.loads(message.value.decode())
                logger.info(f"Received message: {value}")
                
                # Validate transaction
                try:
                    transaction = TransactionBase(**value)
                except Exception as e:
                    logger.error(f"Invalid transaction: {str(e)}")
                    MESSAGES_INVALID.inc()
                    
                    # Publish to invalid topic
                    await producer.send_and_wait(
                        KAFKA_TOPIC_INVALID,
                        json.dumps({
                            "transaction": value,
                            "error": str(e),
                            "timestamp": datetime.now().isoformat()
                        }).encode()
                    )
                    continue
                
                # Enrich transaction
                enriched_transaction = enrich_transaction(transaction.dict())
                
                # Forward to model service
                try:
                    MODEL_REQUESTS.inc()
                    response = requests.post(
                        MODEL_SERVICE_URL,
                        json={"transactions": [enriched_transaction]}
                    )
                    response.raise_for_status()
                    result = response.json()
                    
                    # Check for high risk
                    if result["results"] and result["results"][0]["risk"] >= RISK_THRESHOLD:
                        HIGH_RISK_ALERTS.inc()
                        await send_alert(enriched_transaction, result["results"][0]["risk"])
                    
                    # Broadcast to websocket clients
                    await broadcast_transaction(enriched_transaction, result["results"][0]["risk"])
                    
                except Exception as e:
                    logger.error(f"Error calling model service: {str(e)}")
                    MODEL_ERRORS.inc()
            
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")
            
            finally:
                processing_time = time.time() - start_time
                PROCESSING_TIME.observe(processing_time)
    
    except Exception as e:
        logger.error(f"Fatal error in message processing loop: {str(e)}")
        # Attempt to restart consumer
        await consumer.stop()
        await consumer.start()
        asyncio.create_task(process_messages())

async def send_alert(transaction: Dict[str, Any], risk_score: float):
    """Send alert for high-risk transaction"""
    if not SLACK_WEBHOOK_URL:
        logger.info(f"High risk transaction detected (risk={risk_score}), but no webhook URL configured")
        return
    
    try:
        message = {
            "text": f"🚨 *HIGH RISK TRANSACTION DETECTED*",
            "blocks": [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": "🚨 HIGH RISK TRANSACTION DETECTED"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Transaction ID:*\n{transaction.get('transaction_id', 'Unknown')}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Risk Score:*\n{risk_score:.4f}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Amount:*\n${transaction.get('amount', 0):,.2f}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Type:*\n{transaction.get('type', 'Unknown')}"
                        }
                    ]
                }
            ]
        }
        
        response = requests.post(
            SLACK_WEBHOOK_URL,
            json=message
        )
        response.raise_for_status()
        logger.info(f"Alert sent for high risk transaction (risk={risk_score})")
    
    except Exception as e:
        logger.error(f"Error sending alert: {str(e)}")

@app.websocket("/ws/txns")
async def websocket_endpoint(websocket):
    """WebSocket endpoint for real-time transaction updates"""
    await websocket.accept()
    websocket_clients.add(websocket)
    
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except Exception:
        # Client disconnected
        pass
    finally:
        websocket_clients.remove(websocket)

async def broadcast_transaction(transaction: Dict[str, Any], risk_score: float):
    """Broadcast transaction to all connected WebSocket clients"""
    if not websocket_clients:
        return
    
    # Prepare message
    message = {
        "transaction": transaction,
        "risk_score": risk_score,
        "timestamp": datetime.now().isoformat()
    }
    
    # Send to all clients
    for client in websocket_clients.copy():
        try:
            await client.send_json(message)
        except Exception:
            # Client may have disconnected
            websocket_clients.discard(client)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9000)
